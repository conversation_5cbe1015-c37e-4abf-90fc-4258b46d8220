<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入名称" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1552958167410')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon><reload-outlined /></template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <!-- 名称列 -->
                    <template v-if="column.dataIndex === 'name'">
                        <a @click="handleDetail(record)">{{ record.name }}</a>
                    </template>
                    <!-- 奖惩分类列 -->
                    <template v-if="column.dataIndex === 'typeId'">
                        {{ initDictData['EMPLOYEE_REWARDS_AND_PUNISHMENTS']?.[record.typeId] }}
                    </template>
                    <!-- 操作列 -->
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <a v-if="$config.auth('1552958167410')" @click="handleEdit(record)">{{ $t('common.edit')
                            }}</a>
                            <SkDivider type="vertical" />
                            <SkPopconfirm v-if="$config.auth('1552957567975')" :title="$t('common.deleteConfirm')"
                                @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                :cancelText="$t('common.cancel')">
                                <a class="danger-link">{{ $t('common.delete') }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <!-- 弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle" :width="'90%'">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @saveData="saveData" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { SearchOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()
const tableReady = ref(false)

// Props 定义
const props = defineProps({
    objectId: {
        type: [String, Number],
        required: true
    },
    objectKey: {
        type: String,
        required: true
    }
})

// 保存数据
const saveData = (callback) => {
    callback({
        objectId: props.objectId,
        objectKey: props.objectKey
    })
}


// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列定义
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '名称',
        dataIndex: 'name',
        width: 120
    },
    {
        title: '奖惩金额',
        dataIndex: 'price',
        width: 100
    },
    {
        title: '奖惩时间',
        dataIndex: 'rewardPunishTime',
        width: 100,
        align: 'center'
    },
    {
        title: '奖惩单位',
        dataIndex: 'awardUnit',
        width: 100
    },
    {
        title: '奖惩分类',
        dataIndex: 'typeId',
        width: 100
    },
    {
        title: t('common.createName'),
        dataIndex: 'createName',
        width: 120
    },
    {
        title: t('common.createTime'),
        dataIndex: 'createTime',
        width: 150,
        align: 'center'
    },
    {
        title: t('common.lastUpdateName'),
        dataIndex: 'lastUpdateName',
        width: 120
    },
    {
        title: t('common.lastUpdateTime'),
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: t('common.operation'),
        key: 'action',
        fixed: 'right',
        width: 150,
        align: 'center'
    }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})

// 初始化字典数据
const initDictData = ref({})

const getInitData = async () => {
    let dictResult = await proxy.$util.getDictListMapByCode(['EMPLOYEE_REWARDS_AND_PUNISHMENTS'])
    initDictData.value = dictResult
}

// 获取表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || '',
            objectId: props.objectId,
            objectKey: props.objectKey
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().ehrBasePath + 'queryRewardPunishList',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}

// 新增
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023120300001',
        params: {
            objectId: props.objectId,
            objectKey: props.objectKey
        }
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023120300002',
        params: {
            id: record.id,
            objectId: props.objectId,
            objectKey: props.objectKey
        }
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023120300003',
        params: {
            id: record.id,
            objectId: props.objectId,
            objectKey: props.objectKey
        }
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        await proxy.$http.delete(
            proxy.$config.getConfig().ehrBasePath + 'deleteRewardPunishById',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        fetchData()
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 弹窗确认方法
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    fetchData()
})
</script>

<style scoped></style>