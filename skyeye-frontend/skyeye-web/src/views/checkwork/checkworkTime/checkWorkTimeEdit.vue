<template>
    <div class="container-edit">
        <a-form ref="formRef" :model="formData" :rules="rules" @finish="handleSubmit" v-if="formReady">
            <!-- 基本信息 -->
            <SkHrTitle>基本信息</SkHrTitle>
            <a-row>
                <a-col :span="24">
                    <a-form-item label="标题" name="name" required>
                        <SkInput v-model="formData.name" maxlength="50" placeholder="请输入标题" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="类型" name="checkWorkTimeType" required>
                        <a-radio-group v-model="formData.checkWorkTimeType" @change="handleTypeChange">
                            <template v-for="item in checkWorkTimeTypeOptions" :key="item.id">
                                <a-radio :value="item.id">{{ item.name }}</a-radio>
                            </template>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="状态" name="enabled" required>
                        <a-radio-group v-model="formData.enabled">
                            <template v-for="item in enabledOptions" :key="item.id">
                                <a-radio :value="item.id">{{ item.name }}</a-radio>
                            </template>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item :label="$t('oa.checkWork.workdays')" class="layui-form-item">
                        <div class="layui-input-block">
                            <div class="layui-form-mid layui-word-aux ver-center" style="padding-top: 8px !important">
                                <a-tag color="blue" style="height: 12px;"></a-tag>{{ $t('oa.checkWork.workEveryDay')
                                }}<br>
                                <a-tag color="default" style="height: 12px;"></a-tag>{{
                                    $t('oa.checkWork.restEveryDay') }}<br>
                                <a-tag color="orange" style="height: 12px;"></a-tag>{{
                                    $t('oa.checkWork.singleWeekWork') }}
                            </div>
                        </div>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 工作时间 -->
            <SkHrTitle>工作时间</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="开始时间" name="startTime" required>
                        <a-time-picker v-model:value="formData.startTime" format="HH:mm" :minute-step="30"
                            :disabled-time="disabledStartTime" @change="handleStartTimeChange" placeholder="请选择开始时间" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="结束时间" name="endTime" required>
                        <a-time-picker v-model:value="formData.endTime" format="HH:mm" :minute-step="30"
                            :disabled-time="disabledEndTime" @change="handleEndTimeChange" placeholder="请选择结束时间" />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 作息时间 -->
            <SkHrTitle>作息时间</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="开始时间" name="restStartTime">
                        <a-time-picker v-model:value="formData.restStartTime" format="HH:mm" :minute-step="5"
                            :disabled-time="disabledRestStartTime" @change="handleRestStartTimeChange"
                            placeholder="请选择开始时间" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="结束时间" name="restEndTime">
                        <a-time-picker v-model:value="formData.restEndTime" format="HH:mm" :minute-step="5"
                            :disabled-time="disabledRestEndTime" @change="handleRestEndTimeChange"
                            placeholder="请选择结束时间" />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 按钮 -->
            <div class="form-actions">
                <a-space>
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" html-type="submit">保存</a-button>
                </a-space>
            </div>
        </a-form>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const props = defineProps({
    id: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['submit', 'cancel'])
const { t } = useI18n()
const formRef = ref()
const formReady = ref(false)

// 表单数据
const formData = reactive({
    name: '',
    checkWorkTimeType: '1',
    enabled: '1',
    startTime: null,
    endTime: null,
    restStartTime: null,
    restEndTime: null,
    weekDaySettings: []
})

// 选项数据
const checkWorkTimeTypeOptions = ref([])
const enabledOptions = ref([])

// 获取详情数据
const getDetail = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().checkworkBasePath + 'queryCheckWorkTimeById',
            { params: { id: props.id } }
        )

        if (res.returnCode === 0 && res.bean) {
            // 转换时间格式
            const data = res.bean
            formData.name = data.name
            formData.checkWorkTimeType = data.type
            formData.enabled = data.enabled
            formData.startTime = dayjs(data.startTime, 'HH:mm')
            formData.endTime = dayjs(data.endTime, 'HH:mm')
            formData.restStartTime = data.restStartTime ? dayjs(data.restStartTime, 'HH:mm') : null
            formData.restEndTime = data.restEndTime ? dayjs(data.restEndTime, 'HH:mm') : null

            // 设置工作日
            resetWeekDays(data.type, data.checkWorkTimeWeekList)
            formReady.value = true
        }
    } catch (error) {
        console.error('获取详情失败:', error)
        SkMessage.error('获取详情失败')
    }
}

// 重置工作日设置
const resetWeekDays = (type, weekList = null) => {
    if (weekList && type === '4') {
        formData.weekDaySettings = JSON.parse(weekList)
        return
    }

    switch (type) {
        case '1': // 单休
            formData.weekDaySettings = Array(6).fill().map((_, i) => ({
                weekNumber: i + 1,
                type: 1
            }))
            break
        case '2': // 双休
            formData.weekDaySettings = Array(5).fill().map((_, i) => ({
                weekNumber: i + 1,
                type: 1
            }))
            break
        case '3': // 单双休
            formData.weekDaySettings = [
                ...Array(5).fill().map((_, i) => ({ weekNumber: i + 1, type: 1 })),
                { weekNumber: 6, type: 2 }
            ]
            break
        case '4': // 自定义
            formData.weekDaySettings = []
            break
    }
}

// 提交处理
const handleSubmit = async () => {
    try {
        if (formData.weekDaySettings.length === 0) {
            SkMessage.error('请选择工作日')
            return
        }

        const params = {
            id: props.id,
            name: formData.name,
            startTime: dayjs(formData.startTime).format('HH:mm'),
            endTime: dayjs(formData.endTime).format('HH:mm'),
            restStartTime: formData.restStartTime ? dayjs(formData.restStartTime).format('HH:mm') : '',
            restEndTime: formData.restEndTime ? dayjs(formData.restEndTime).format('HH:mm') : '',
            type: formData.checkWorkTimeType,
            enabled: formData.enabled,
            checkWorkTimeWeekList: JSON.stringify(formData.weekDaySettings)
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().checkworkBasePath + 'writeCheckWorkTime',
            params
        )

        if (res.returnCode === 0) {
            SkMessage.success('保存成功')
            emit('submit')
        }
    } catch (error) {
        console.error('保存失败:', error)
        SkMessage.error(error.message || '保存失败')
    }
}

// 初始化
onMounted(async () => {
    // 获取选项数据
    const [typeOptions, enableOptions] = await Promise.all([
        proxy.$util.getEnumListByCode('checkWorkTimeType'),
        proxy.$util.getEnumListByCode('commonEnable')
    ])

    checkWorkTimeTypeOptions.value = typeOptions
    enabledOptions.value = enableOptions

    // 获取详情数据
    await getDetail()
})

// ... 其他方法（时间处理、工作日处理等）与新增页面相同 ...
</script>

<style scoped></style>
