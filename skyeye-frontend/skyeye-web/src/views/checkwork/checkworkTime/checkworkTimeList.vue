<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 提示信息 -->
            <SkAlert message="已经拥有员工使用的班次信息无法进行删除操作。" type="info" show-icon />

            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入标题" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 表格工具栏 -->
            <div class="table-operations">
                <SkSpace>
                    <!-- 添加 -->
                    <SkButton v-if="$config.auth('1603026174350')" type="primary" @click="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>

                    <!-- 刷新 -->
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t("common.refresh") }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :dataSource="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'name'">
                        <a @click="handleDetails(record)">{{ record.name }}</a>
                    </template>
                    <template v-if="column.dataIndex === 'TimeType'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['checkWorkTimeType'], 'id', record.type, 'name')">
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['commonEnable'], 'id', record.enabled, 'name')">
                        </div>
                    </template>

                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <!-- 编辑 -->
                            <a v-if="$config.auth('1603026174350')" @click="handleEdit(record)">
                                {{ $t("common.edit") }}
                            </a>
                            <SkDivider v-if="$config.auth('1603026174350')" type="vertical" />
                            <a v-if="$config.auth('1714276416911')" @click="OnlineClockInformation(record)">
                                {{ $t("common.onlineclockInformation") }}
                            </a>
                            <SkDivider v-if="$config.auth('1714276416911')" type="vertical" />
                            <!-- 删除 -->
                            <SkPopconfirm :title="$t('common.deleteConfirm')" @confirm="handleDelete(record)"
                                :okText="$t('common.delete')" :cancelText="$t('common.cancel')">
                                <a class="danger-link">{{ $t("common.delete") }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <!-- 弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle" :width="modalWidth" @cancel="handleModalCancel">
                <ShowIndex ref="showIndexRef" v-if="
                    modalType === 'onlineClockInformation'
                " :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick">
                </ShowIndex>
                <CheckWorkTimeAdd v-if="modalType === 'add'" ref="formRef" @submit="handleModalClick"
                    @cancel="handleModalCancel">
                </CheckWorkTimeAdd>
                <CheckWorkTimeEdit v-if="modalType === 'edit'" ref="formRef" @submit="handleModalClick"
                    @cancel="handleModalCancel">
                </CheckWorkTimeEdit>
                <CheckWorkTimeDetails v-if="modalType === 'details'" ref="formRef" :id="tableDataId.id"
                    @submit="handleModalClick" @cancel="handleModalCancel">
                </CheckWorkTimeDetails>
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'
import { SearchOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import { useI18n } from 'vue-i18n'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import CheckWorkTimeAdd from '@/views/checkwork/checkworkTime/checkWorkTimeAdd.vue'
import CheckWorkTimeEdit from '@/views/checkwork/checkworkTime/checkWorkTimeEdit.vue'
import CheckWorkTimeDetails from '@/views/checkwork/checkworkTime/checkWorkTimeDetails.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()
const tableReady = ref(false)
const cardRef = ref(null)
const formRef = ref(null)

// 搜索表单
const searchForm = reactive({
    keyword: '',
})

// 表单数据
const formData = reactive({
    name: '',
    startTime: '',
    endTime: '',
    restStartTime: '',
    restEndTime: '',
    TimeType: '',
    state: ''
})

// 表单验证规则
// const rules = {
//     name: [{ required: true, message: '请输入标题' }],
//     startTime: [{ required: true, message: '请选择工作开始时间' }],
//     endTime: [{ required: true, message: '请选择工作结束时间' }],
//     restStartTime: [{ required: true, message: '请选择休息开始时间' }],
//     restEndTime: [{ required: true, message: '请选择休息结束时间' }],
//     TimeType: [{ required: true, message: '请选择类型' }],
//     state: [{ required: true, message: '请选择状态' }]
// }

// 表格列定义
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '标题',
        dataIndex: 'name',
        width: 100
    },
    {
        title: '工作时间段',
        dataIndex: 'startTime',
        width: 120,
        align: 'center',
        customRender: ({ record }) => {
            return `${record.startTime} ~ ${record.endTime}`
        }
    },
    {
        title: '作息时间段',
        dataIndex: 'restStartTime',
        width: 120,
        align: 'center',
        customRender: ({ record }) => {
            return `${record.restStartTime} ~ ${record.restEndTime}`
        }
    },
    {
        title: '类型',
        dataIndex: 'TimeType',
        width: 90,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 80,
        align: 'center',
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 120,
        align: 'left'
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 160,
        align: 'left'
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 140
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: t('common.operation'),
        key: 'action',
        width: 150,
        fixed: 'right'
    }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const modalVisible = ref(false)
const initEnumData = ref({})
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})
const tableDataId = reactive({
    id: ''
})

const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['commonEnable', 'checkWorkTimeType']);
    initEnumData.value = enumResult
}

const OnlineClockInformation = (record) => {
    modalTitle.value = '线上打卡信息'
    modalType.value = "onlineClockInformation"
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024042800001',
        params: {
            id: record.id,
            name: record.name,
            startTime: record.startTime,
            endTime: record.endTime,
            restStartTime: record.restStartTime,
            restEndTime: record.restEndTime,
            TimeType: record.TimeType,
        }
    }
}

// 模态框相关
const modalWidth = ref(800)

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 5),
            keyword: searchForm.keyword?.trim()
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().checkworkBasePath + 'checkworktime001',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}

// 新增
const handleAdd = () => {
    modalTitle.value = '新增班次'
    modalType.value = 'add'
    modalVisible.value = true
}

// 详情
const handleDetails = (record) => {
    tableDataId.id = record.id
    modalTitle.value = '班次详情'
    modalType.value = 'details'
    modalVisible.value = true
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = "编辑班次"
    modalType.value = "edit"
    modalVisible.value = true
}

// 删除
const handleDelete = async (record) => {
    try {
        await proxy.$http.delete(
            proxy.$config.getConfig().checkworkBasePath + 'deleteCheckWorkTimeById',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        fetchData()
    } catch (error) {
        console.error('删除失败:', error)
        SkMessage.error('删除失败')
    }
}

// 处理模态框取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    fetchData()
})
</script>

<style scoped></style>