<template>
    <div class="container-add">
        <a-form ref="formRef" :model="formData" :rules="rules" @finish="handleSubmit">
            <!-- 基本信息 -->
            <SkHrTitle>基本信息</SkHrTitle>
            <a-row>
                <a-col :span="24">
                    <a-form-item label="标题" name="name" required>
                        <SkInput v-model="formData.name" maxlength="50" placeholder="请输入标题" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="类型" name="checkWorkTimeType" required>
                        <a-radio-group v-model="formData.checkWorkTimeType" @change="handleTypeChange">
                            <template v-for="item in checkWorkTimeTypeOptions" :key="item.id">
                                <a-radio :value="item.id">{{ item.name }}</a-radio>
                            </template>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="状态" name="enabled" required>
                        <a-radio-group v-model="formData.enabled">
                            <template v-for="item in enabledOptions" :key="item.id">
                                <a-radio :value="item.id">{{ item.name }}</a-radio>
                            </template>
                        </a-radio-group>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="工作日" required>
                        <div class="week-days">
                            <a-tag 
                                v-for="day in weekDays" 
                                :key="day.value"
                                :color="getWeekDayColor(day.value)"
                                @click="handleWeekDayClick(day.value)"
                                class="week-day-tag"
                            >
                                {{ day.label }}
                            </a-tag>
                        </div>
                        <div class="week-days-legend">
                            <a-tag color="blue">每周的当天都工作</a-tag>
                            <a-tag color="default">每周的当天都休假</a-tag>
                            <a-tag color="orange">单周上班，双周休假</a-tag>
                        </div>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 工作时间 -->
            <SkHrTitle>工作时间</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="开始时间" name="startTime" required>
                        <a-time-picker 
                            v-model:value="formData.startTime"
                            format="HH:mm"
                            :minute-step="30"
                            :disabled-time="disabledStartTime"
                            @change="handleStartTimeChange"
                            placeholder="请选择开始时间"
                        />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="结束时间" name="endTime" required>
                        <a-time-picker 
                            v-model:value="formData.endTime"
                            format="HH:mm"
                            :minute-step="30"
                            :disabled-time="disabledEndTime"
                            @change="handleEndTimeChange"
                            placeholder="请选择结束时间"
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 作息时间 -->
            <SkHrTitle>作息时间</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="开始时间" name="restStartTime">
                        <a-time-picker 
                            v-model:value="formData.restStartTime"
                            format="HH:mm"
                            :minute-step="5"
                            :disabled-time="disabledRestStartTime"
                            @change="handleRestStartTimeChange"
                            placeholder="请选择开始时间"
                        />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="结束时间" name="restEndTime">
                        <a-time-picker 
                            v-model:value="formData.restEndTime"
                            format="HH:mm"
                            :minute-step="5"
                            :disabled-time="disabledRestEndTime"
                            @change="handleRestEndTimeChange"
                            placeholder="请选择结束时间"
                        />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 按钮 -->
            <div class="form-actions">
                <a-space>
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" html-type="submit">保存</a-button>
                </a-space>
            </div>
        </a-form>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const { t } = useI18n()
const emit = defineEmits(['submit', 'cancel'])
const formRef = ref()

// 表单数据
const formData = reactive({
    name: '',
    checkWorkTimeType: '1',
    enabled: '1',
    startTime: null,
    endTime: null,
    restStartTime: null,
    restEndTime: null,
    weekDaySettings: []
})

// 校验规则
const rules = {
    name: [{ required: true, message: '请输入标题' }],
    checkWorkTimeType: [{ required: true, message: '请选择类型' }],
    enabled: [{ required: true, message: '请选择状态' }],
    startTime: [{ required: true, message: '请选择开始时间' }],
    endTime: [{ required: true, message: '请选择结束时间' }]
}

// 工作日配置
const weekDays = [
    { label: '一', value: '1' },
    { label: '二', value: '2' },
    { label: '三', value: '3' },
    { label: '四', value: '4' },
    { label: '五', value: '5' },
    { label: '六', value: '6' },
    { label: '日', value: '7' }
]

// 类型变化处理
const handleTypeChange = (e) => {
    const type = e.target.value
    resetWeekDays(type)
}

// 重置工作日设置
const resetWeekDays = (type) => {
    switch (type) {
        case '1': // 单休
            formData.weekDaySettings = Array(6).fill().map((_, i) => ({
                weekNumber: i + 1,
                type: 1
            }))
            break
        case '2': // 双休
            formData.weekDaySettings = Array(5).fill().map((_, i) => ({
                weekNumber: i + 1,
                type: 1
            }))
            break
        case '3': // 单双休
            formData.weekDaySettings = [
                ...Array(5).fill().map((_, i) => ({ weekNumber: i + 1, type: 1 })),
                { weekNumber: 6, type: 2 }
            ]
            break
        case '4': // 自定义
            formData.weekDaySettings = []
            break
    }
}

// 获取工作日颜色
const getWeekDayColor = (value) => {
    const setting = formData.weekDaySettings.find(s => s.weekNumber === value)
    if (!setting) return 'default'
    return setting.type === 1 ? 'blue' : setting.type === 2 ? 'orange' : 'default'
}

// 工作日点击处理
const handleWeekDayClick = (value) => {
    if (formData.checkWorkTimeType !== '4') return
    
    const index = formData.weekDaySettings.findIndex(s => s.weekNumber === value)
    if (index === -1) {
        formData.weekDaySettings.push({ weekNumber: value, type: 1 })
    } else {
        const setting = formData.weekDaySettings[index]
        if (setting.type === 1) {
            setting.type = 2
        } else if (setting.type === 2) {
            formData.weekDaySettings.splice(index, 1)
        }
    }
}

// 提交处理
const handleSubmit = async () => {
    try {
        if (formData.weekDaySettings.length === 0) {
            SkMessage.error('请选择工作日')
            return
        }

        const params = {
            name: formData.name,
            startTime: dayjs(formData.startTime).format('HH:mm'),
            endTime: dayjs(formData.endTime).format('HH:mm'),
            restStartTime: formData.restStartTime ? dayjs(formData.restStartTime).format('HH:mm') : '',
            restEndTime: formData.restEndTime ? dayjs(formData.restEndTime).format('HH:mm') : '',
            type: formData.checkWorkTimeType,
            enabled: formData.enabled,
            checkWorkTimeWeekList: JSON.stringify(formData.weekDaySettings)
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().checkworkBasePath + 'writeCheckWorkTime',
            params
        )

        if (res.returnCode === 0) {
            SkMessage.success('保存成功')
            emit('submit')
        } else {
            throw new Error(res.returnMessage || '保存失败')
        }
    } catch (error) {
        console.error('保存失败:', error)
        SkMessage.error(error.message || '保存失败')
    }
}

// 取消处理
const handleCancel = () => {
    emit('cancel')
}

// 初始化
onMounted(async () => {
    // 获取类型和状态选项
    const [checkWorkTimeTypeOptions, enabledOptions] = await Promise.all([
        proxy.$util.getEnumListByCode('checkWorkTimeType'),
        proxy.$util.getEnumListByCode('commonEnable')
    ])
    
    // 设置默认工作日
    resetWeekDays('1')
})
</script>

<style scoped>
</style>
