<template>
    <div class="container-details">
        <SkForm ref="formRef" v-model="formData" :showButtons="false">
            <!-- 基本信息 -->
            <SkHrTitle>基本信息</SkHrTitle>
            <a-row>
                <a-col :span="24">
                    <a-form-item label="标题">
                        <div class="form-content">{{ formData.name }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="类型">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['checkWorkTimeType'], 'id', formData.type, 'name')">
                        </div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="状态">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['commonEnable'], 'id', formData.enabled, 'name')">
                        </div>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <!-- 打卡表单 -->
                    <a-form layout="vertical">
                        <a-form-item :label="$t('oa.checkWork.workdays')" class="layui-form-item">
                            <div class="layui-input-block">
                                <a-tag v-for="day in weekDays" :key="day.value" :color="getWeekDayColor(day.value)"
                                    class="weekDay">
                                    {{ day.label }}
                                </a-tag>
                                <div class="layui-form-mid layui-word-aux ver-center"
                                    style="padding-top: 8px !important">
                                    <a-tag color="blue" style="height: 12px;"></a-tag>{{ $t('oa.checkWork.workEveryDay')
                                    }}<br>
                                    <a-tag color="default" style="height: 12px;"></a-tag>{{
                                        $t('oa.checkWork.restEveryDay') }}<br>
                                    <a-tag color="orange" style="height: 12px;"></a-tag>{{
                                        $t('oa.checkWork.singleWeekWork') }}
                                </div>
                            </div>
                        </a-form-item>
                    </a-form>
                </a-col>
            </a-row>

            <!-- 工作时间 -->
            <SkHrTitle>工作时间</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="开始时间">
                        <div class="form-content">{{ formData.startTime }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="结束时间">
                        <div class="form-content">{{ formData.endTime }}</div>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 作息时间 -->
            <SkHrTitle>作息时间</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="开始时间">
                        <div class="form-content">{{ formData.restStartTime || '-' }}</div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="结束时间">
                        <div class="form-content">{{ formData.restEndTime || '-' }}</div>
                    </a-form-item>
                </a-col>
            </a-row>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'

const props = defineProps({
    id: {
        type: String,
        default: ''
    }
})

const { proxy } = getCurrentInstance()
const formData = reactive({})
const initEnumData = ref({
    commonEnable: [],
    checkWorkTimeType: []
})

// 获取详情数据
const getDetail = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().checkworkBasePath + 'queryCheckWorkTimeById',
            { id: props.id }
        )

        if (res.bean) {
            Object.assign(formData, res.bean)
        }
    } catch (error) {
        SkMessage.error('获取车间任务详情失败')
    }
}

const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['commonEnable', 'checkWorkTimeType']);
    initEnumData.value = enumResult
}

// 初始化
onMounted(async () => {
    await getInitData()
    await getDetail()
})
</script>

<style scoped></style>
