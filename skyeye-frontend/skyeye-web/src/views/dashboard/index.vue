<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="welcome-card">
                <h2>欢迎回来，{{ username }}</h2>
                <p>今天是 {{ currentDate }}</p>
            </div>

            <a-row :gutter="16" class="statistics">
                <a-col :span="6">
                    <a-card>
                        <template #title>
                            本月考勤
                            <SkTag class="header-right-top" color="blue">天</SkTag>
                        </template>
                        <div class="card-body">
                            <p class="big-font">{{ statistics.checkOnWorkNum }}</p>
                            <p class="desc" title="合计已正常完成上下班打卡的考勤天数">
                                合计已正常完成上下班打卡的考勤天数
                            </p>
                        </div>
                    </a-card>
                </a-col>

                <a-col :span="6">
                    <a-card>
                        <template #title>
                            我的文件数
                            <SkTag class="header-right-top" color="green">个</SkTag>
                        </template>
                        <div class="card-body">
                            <p class="big-font">{{ statistics.diskCloudFileNum }}</p>
                            <p class="desc" title="文件管理里由我上传的所有文件数">
                                文件管理里由我上传的所有文件数
                            </p>
                        </div>
                    </a-card>
                </a-col>

                <a-col :span="6">
                    <a-card>
                        <template #title>
                            我的论坛帖
                            <SkTag class="header-right-top" color="orange">篇</SkTag>
                        </template>
                        <div class="card-body">
                            <p class="big-font">{{ statistics.forumNum }}</p>
                            <p class="desc" title="我发表的论坛帖">我发表的论坛帖</p>
                        </div>
                    </a-card>
                </a-col>

                <a-col :span="6">
                    <a-card>
                        <template #title>
                            我的知识库文章
                            <SkTag class="header-right-top">篇</SkTag>
                        </template>
                        <div class="card-body">
                            <p class="big-font">{{ statistics.knowledgeNum }}</p>
                            <p class="desc" title="已审批通过的知识库文章">已审批通过的知识库文章</p>
                        </div>
                    </a-card>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="16">
                    <a-card>
                        <a-tabs v-model:activeKey="activeTab1">
                            <a-tab-pane key="1" tab="公告">
                                <a-table :columns="noticeColumns" :data-source="noticeList" :pagination="false">
                                    <template #bodyCell="{ column, record }">
                                        <template v-if="column.key === 'name'">
                                            <a @click="showNoticeDetail(record.id)">{{ record.name }}</a>
                                        </template>
                                    </template>
                                </a-table>
                            </a-tab-pane>
                        </a-tabs>
                    </a-card>

                    <a-card class="mt-4">
                        <a-tabs v-model:activeKey="activeTab2">
                            <a-tab-pane key="1" tab="最新知识库">
                                <a-table :columns="knowledgeColumns" :data-source="knowledgeList" :pagination="false">
                                    <template #bodyCell="{ column, record }">
                                        <template v-if="column.key === 'name'">
                                            <a @click="showKnowledgeDetail(record.id)">{{ record.name }}</a>
                                        </template>
                                    </template>
                                </a-table>
                            </a-tab-pane>
                        </a-tabs>
                    </a-card>
                </a-col>

                <a-col :span="8">
                    <a-card title="热门论坛帖">
                        <a-list :data-source="forumList" class="forum-list">
                            <template #renderItem="{ item }">
                                <a-list-item @click="showForumDetail(item.id)">
                                    <a-list-item-meta>
                                        <template #title>{{ item.forumTitle }}</template>
                                        <template #description>{{ item.forumContent }}</template>
                                    </a-list-item-meta>
                                    <div>{{ item.createTime }}</div>
                                </a-list-item>
                            </template>
                        </a-list>
                    </a-card>
                </a-col>
            </a-row>
        </SkCard>
        <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
            <ShowIndex v-if="pageType === 'show'" ref="showIndexRef" :pageId="operatorParams.pageId"
                :params="operatorParams.params">
            </ShowIndex>
            <ForumItem v-else ref="forumItemRef" :forum-id="selectedId" :current-user="userInfo" />
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import ForumItem from '@/views/oa/forumShow/forumItem.vue'

const { proxy } = getCurrentInstance()

const username = ref('Admin')
const currentDate = ref('')

// 统计数据
const statistics = ref({
    checkOnWorkNum: 0,
    diskCloudFileNum: 0,
    forumNum: 0,
    knowledgeNum: 0
})

// 表格列定义
const noticeColumns = [
    { title: '主题', dataIndex: 'name', key: 'name' },
    { title: '类型', dataIndex: 'createName', width: 120 },
    { title: '时间', dataIndex: 'realLinesTime', width: 180 }
]

const knowledgeColumns = [
    { title: '主题', dataIndex: 'name', key: 'name' },
    { title: '类型', dataIndex: ['typeMation', 'dictName'], width: 150 },
    { title: '时间', dataIndex: 'createTime', width: 180 }
]

// 数据列表
const noticeList = ref([])
const knowledgeList = ref([])
const forumList = ref([])

// 标签页激活key
const activeTab1 = ref('1')
const activeTab2 = ref('1')

onMounted(() => {
    const date = new Date()
    currentDate.value = date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    })
    username.value = proxy.$config.getCurrentUser()?.userName
    initStatistics()
})

// 获取统计数据
const initStatistics = async () => {
    try {
        // 发送提交请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().reqBasePath + 'mainpage001'
        )
        statistics.value = res.bean
        await Promise.all([
            initNoticeList(),
            initForumList(),
            initKnowledgeList()
        ])
    } catch (error) {
        SkMessage.error('获取统计数据失败')
    }
}

// 获取公告列表
const initNoticeList = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().noticeBasePath + 'queryUserReceivedTopNotice'
        )
        noticeList.value = res.rows
    } catch (error) {
        SkMessage.error('获取公告列表失败')
    }
}

// 获取论坛列表
const initForumList = async () => {
    try {
        const res = await proxy.$http.post(
            proxy.$config.getConfig().forumBasePath + 'queryHotForumList', {
            limit: 10,
            page: 1
        })
        forumList.value = res.rows
    } catch (error) {
        SkMessage.error('获取论坛列表失败')
    }
}

// 获取知识库列表
const initKnowledgeList = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().knowlgBasePath + 'queryEightPassKnowlgList'
        )
        knowledgeList.value = res.rows
        statistics.value.knowledgeNum = res.total
    } catch (error) {
        SkMessage.error('获取知识库列表失败')
    }
}

const modalVisible = ref(false)
const modalTitle = ref('')
const modalWidth = ref('70%')
const operatorParams = ref({
    pageId: 'noticeDetail',
    params: {}
})
const pageType = ref('show')
const selectedId = ref('')
const userInfo = ref({})

// 查看详情方法
const showNoticeDetail = (id) => {
    modalVisible.value = true
    modalTitle.value = '公告详情'
    pageType.value = 'show'
    operatorParams.value = {
        pageId: 'FP2024013100006',
        params: {
            id: id
        }
    }
}

const showKnowledgeDetail = (id) => {
    modalVisible.value = true
    modalTitle.value = '知识库详情'
    pageType.value = 'show'
    operatorParams.value = {
        pageId: 'FP2023101500004',
        params: {
            id: id
        }
    }
}

const showForumDetail = (id) => {
    modalVisible.value = true
    modalTitle.value = '论坛详情'
    pageType.value = 'forum'
    selectedId.value = id
    userInfo.value = proxy.$config.getCurrentUser()
}
</script>

<style scoped>
.welcome-card {
    margin-bottom: 24px;
    padding: 24px;
    background: linear-gradient(135deg, #1890ff 0%, #1890ff 100%);
    border-radius: 4px;
    color: white;
}

.welcome-card h2 {
    margin: 0;
    font-size: 24px;
}

.welcome-card p {
    margin: 8px 0 0;
    opacity: 0.8;
}

.statistics {
    margin-bottom: 24px;
}

.card-body {
    padding: 20px;
}

.big-font {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
}

.desc {
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.header-right-top {
    float: right;
}

.mt-4 {
    margin-top: 16px;
}

.forum-list {
    height: 350px;
    overflow-y: auto;
}

:deep(.ant-card-head) {
    border-bottom: none;
}

:deep(.ant-card-body) {
    overflow-x: hidden !important;
}

:deep(.ant-list-item) {
    display: block;
}
</style>