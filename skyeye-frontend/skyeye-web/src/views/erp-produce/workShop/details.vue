<template>
    <div class="container-manage">
        <SkForm ref="formRef" v-model="formData" :showButtons="false">
            <SkHrTitle>基本信息</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="车间">
                        {{ formData.farmMation?.name }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="任务编号">
                        {{ formData.oddNumber }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="状态">
                        <span v-if="formData.state" :style="{ color: getStateColor(formData.state) }">
                            {{ getStateName(formData.state) }}
                        </span>
                        <span v-else>-</span>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="任务数量">
                        {{ formData.targetNum }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="创建时间">
                        {{ formData.createTime }}
                    </a-form-item>
                </a-col>
            </a-row>
            <SkHrTitle>车间信息</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="车间名称">
                        {{ formData.farmMation?.name }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="车间编号">
                        {{ formData.farmMation?.number }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="所属部门">
                        {{ formData.farmMation?.departmentMation?.name }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="负责人">
                        {{ formData.farmMation?.chargePersonMation?.userName }}
                    </a-form-item>
                </a-col>
            </a-row>
            <SkHrTitle>工序信息</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="8">
                    <a-form-item label="计划开始">
                        {{ formData.machinProcedureMation?.planStartTime }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="计划结束">
                        {{ formData.machinProcedureMation?.planEndTime }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="实际开始">
                        {{ formData.machinProcedureMation?.actualStartTime }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="实际结束">
                        {{ formData.machinProcedureMation?.actualEndTime }}
                    </a-form-item>
                </a-col>
            </a-row>
            <SkHrTitle>加工单信息</SkHrTitle>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="加工单号">
                        <a @click="workDetails(formData.machinId)">
                            {{ $util.isNull(formData.machinMation?.fromId) ? formData.machinMation?.oddNumber :
                                formData.machinMation?.oddNumber + '[转]' }}
                        </a>
                    </a-form-item>
                </a-col>
            </a-row>
        </SkForm>

        <!-- 加工单详情弹窗 -->
        <SkModal v-model="modalVisible" :title="modalTitle">
            <ShowIndex ref="showIndexRef" v-if="modalType === 'machinId'" :pageId="operatorParams.pageId"
                :params="operatorParams.params" :whetherCustomerData="whetherCustomerData" @close="handleModalClose">
            </ShowIndex>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import SkForm from '@/components/SkForm/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'

const props = defineProps({
    objectId: {
        type: String,
        default: ''
    },
    serviceClassName: {
        type: String,
        default: ''
    }
})

const { proxy } = getCurrentInstance()

// 表单数据
const formData = reactive({})

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    const result = await proxy.$util.getEnumListMapByCode(['machinProcedureFarmState'])
    initEnumData.value = result || {}
}

// 获取车间任务详情
const getWorkShopDetails = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'getDataByObjectId',
            {
                objectId: props.objectId,
                serviceClassName: props.serviceClassName
            }
        )
        if (res.bean) {
            Object.assign(formData, res.bean)
        }
    } catch (error) {
        SkMessage.error('获取车间任务详情失败')
    }
}

// 获取状态名称
const getStateName = (stateId) => {
    if (!initEnumData.value.machinProcedureFarmState) return '-'
    const stateItem = initEnumData.value.machinProcedureFarmState.find(item => item.id === stateId)
    return stateItem ? stateItem.name : '-'
}

// 获取状态颜色
const getStateColor = (stateId) => {
    if (!initEnumData.value.machinProcedureFarmState) return ''
    const stateItem = initEnumData.value.machinProcedureFarmState.find(item => item.id === stateId)
    return stateItem ? stateItem.color : ''
}

// 加工单详情弹窗相关变量
const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')
const whetherCustomerData = ref(false)
const operatorParams = ref({})

// 加工单详情
const workDetails = (machinId) => {
    modalTitle.value = '加工单详情'
    modalType.value = 'machinId'
    modalVisible.value = true
    whetherCustomerData.value = false

    operatorParams.value = {
        pageId: 'FP2023100300003',
        params: {
            id: machinId
        }
    }
}

// 弹窗关闭
const handleModalClose = () => {
    modalVisible.value = false
}

// 初始化
onMounted(async () => {
    await getInitData()
    await getWorkShopDetails()
})
</script>

<style scoped></style>