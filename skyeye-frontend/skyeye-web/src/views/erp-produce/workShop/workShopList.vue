<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item>
                        <SkFlex>
                            <div class="content-tag-name">车间</div>
                            <SkSelect v-model="workshopId" showSearch :options="options" placeholder="请选择车间"
                                :filterOption="filterOption" @change="handleStoreChange" :allowClear="false" />
                        </SkFlex>
                    </a-form-item>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入车间编号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t("common.refresh") }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <!-- 任务编号 -->
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleNumberDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <!-- 所属加工单 -->
                    <template v-if="column.dataIndex === 'machinId'">
                        <a @click="handleMachiningDetail(record)">{{ record.machinMation?.oddNumber }}
                            <template v-if="!$util.isNull(record.machinMation?.fromId)">
                                <SkTag :color="$util.getTagColor(column.dataIndex)">转</SkTag>
                            </template>
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(
                            initEnumData['machinProcedureFarmState'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.state == 'waitReceive'">
                                <!-- 接收 -->
                                <SkPopconfirm v-if="$config.auth('1720939511614')" :title="$t('common.acceptConfirm')"
                                    @confirm="handleAccept(record)" :okText="$t('common.accept')"
                                    :cancelText="$t('common.cancel')">
                                    <a>{{ $t("common.accept") }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.state == 'waitExecuted'">
                                <!-- 反接收 -->
                                <SkPopconfirm v-if="$config.auth('1720939488183')" :title="$t('common.unacceptConfirm')"
                                    @confirm="handleUnccept(record)" :okText="$t('common.unaccept')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t("common.unaccept") }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.state == 'waitExecuted' || record.state == 'partialCompletion'">
                                <!-- 工序验收 -->
                                <a v-if="$config.auth('1721013026647')" @click="handleProcessAcceptance(record)">
                                    {{ $t("common.processAcceptance") }}
                                </a>
                            </template>
                            <template v-if="record.state == 'allCompleted' || record.state == 'excessCompleted'">
                                <!-- 加工入库 -->
                                <a v-if="$config.auth('1722236598520') && record.isLastProcedure"
                                    @click="handleWarehousing(record)">
                                    {{ $t("common.warehousing") }}
                                </a>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef" v-if="modalType === 'processAcceptance' || modalType === 'warehousing' || modalType === 'machinId'
                " :pageId="operatorParams.pageId" :params="operatorParams.params"
                    :whetherCustomerData="whetherCustomerData" :customerData="customerData"
                    @customerDataSave="customerDataSave" @close="handleModalClick" @handleChange="handleChange"
                    @afterDataLoaded="handleAfterDataLoaded" @cell-change="handleCellChange">
                    <!-- 商品选择组件 -->
                    <template #cell-chooseInput-materialId="slotProps">
                        <a-form-item-rest>
                            <SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                </ShowIndex>
                <!-- 详情 -->
                <Detail v-if="modalType === 'details'" :objectId="operatorParams.objectId"
                    :serviceClassName="operatorParams.serviceClassName">
                </Detail>
            </SkModal>
        </SkCard>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from "vue";
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { useI18n } from "vue-i18n";
import SkForm from "@/components/SkForm/index.vue";
import SkInput from "@/components/SkInput/index.vue";
import SkCard from "@/components/SkCard/index.vue";
import SkButton from "@/components/SkButton/index.vue";
import SkSpace from "@/components/SkSpace/index.vue";
import SkTable from "@/components/SkTable/index.vue";
import SkPopconfirm from "@/components/SkPopconfirm/index.vue";
import { SkMessage } from "@/components/SkMessage/index.vue";
import SkModal from "@/components/SkModal/index.vue";
import ShowIndex from "@/views/dsForm/show/index.vue";
import Detail from "./details.vue";
import SkFlex from '@/components/SkFlex/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'

const { proxy } = getCurrentInstance();
const { t } = useI18n();

const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    }
})

// 搜索表单数据
const searchForm = reactive({
    keyword: "",
});

// 表格列配置
const columns = ref([
    {
        title: t("common.serialNum"),
        dataIndex: "index",
        width: 80,
        align: "center",
        fixed: "left",
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1;
        },
    },
    {
        title: "任务编号",
        dataIndex: "oddNumber",
        width: 200,
        align: "center",
    },
    {
        title: "状态",
        dataIndex: "state",
        width: 120,
    },
    {
        title: "部门",
        dataIndex: "name",
        width: 150,
        customRender: ({ record }) => {
            return record.farmMation?.departmentMation?.name;
        }
    },
    {
        title: '加工信息',
        align: 'center',
        children: [{
            title: '所属加工单',
            dataIndex: 'machinId',
            width: 150,
            customRender: ({ record }) => record.machinMation?.oddNumber
        }, {
            title: '加工工序',
            dataIndex: 'procedureId',
            width: 140,
            customRender: ({ record }) => record.machinProcedureMation?.procedureMation?.name
        }]
    },
    {
        title: "任务安排数量",
        dataIndex: "targetNum",
        width: 120,
        align: 'center',
    },
    {
        title: '计划时间',
        align: 'center',
        children: [{
            title: '计划开始时间',
            dataIndex: 'planStartTime',
            width: 120,
            customRender: ({ record }) => record.machinProcedureMation?.planStartTime
        }, {
            title: '计划结束时间',
            dataIndex: 'actualEndTime',
            width: 120,
            customRender: ({ record }) => record.machinProcedureMation?.actualEndTime
        }]
    },
    {
        title: '实际时间',
        align: 'center',
        children: [{
            title: '实际开始时间',
            dataIndex: 'actualStartTime',
            width: 120,
            customRender: ({ record }) => record.machinProcedureMation?.actualStartTime
        }, {
            title: '实际结束时间',
            dataIndex: 'actualEndTime',
            width: 120,
            customRender: ({ record }) => record.machinProcedureMation?.actualEndTime
        }]
    },
    {
        title: "创建人",
        dataIndex: "createName",
        width: 140,
    },
    {
        title: "创建时间",
        dataIndex: "createTime",
        width: 150,
        align: "center",
    },
    {
        title: "最后修改人",
        dataIndex: "lastUpdateName",
        width: 140,
    },
    {
        title: "最后修改时间",
        dataIndex: "lastUpdateTime",
        width: 150,
        align: "center",
    },
    {
        title: t('common.operation'),
        key: "action",
        width: 220,
        align: "center",
        fixed: "right",
    },
]);

const tableData = ref([]); // 表格数据
const loading = ref(false); // 加载状态
const tableReady = ref(false);

// 分页配置
const pagination = reactive(proxy.$config.pagination());

// 初始化枚举数据
const initEnumData = ref({});
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(["machinProcedureFarmState"]);
    initEnumData.value = result;
};

// 车间选择
const workshopId = ref('')
const options = ref([])

// 选择车间的处理函数
const handleStoreChange = (value) => {
    workshopId.value = value
    fetchData()
}

// 添加 filterOption 函数
const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 获取车间数据
const fetchWorkShop = async () => {
    try {
        const res = await proxy.$http.get(proxy.$config.getConfig().erpBasePath + 'queryStaffBelongFarmList')
        options.value = res.rows.map(workshop => ({
            value: workshop.id,
            label: workshop.name
        }))
        // 自动选中第一个车间
        workshopId.value = options.value[0]?.value
        await fetchData()
    } catch (error) {
        SkMessage.error('获取门店数据失败')
    }
}

// 获取数据
const fetchData = async () => {
    loading.value = true;
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || "",
            type: 'farm',
            objectId: workshopId.value
        };

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + "queryMachinProcedureFarmList",
            params
        );
        tableData.value = res.rows || [];
        pagination.total = res.total || 0;
    } catch (error) {
        SkMessage.error("获取数据失败");
        tableData.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = ""; // 清空搜索关键字
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1);
        pagination.pageSize = Number(pag.pageSize || 10);
    }
    fetchData();
};

const modalVisible = ref(false);
const modalTitle = ref("审批人选择");
const modalType = ref("approval");
const whetherCustomerData = ref(false);
const customerData = ref({})
const operatorParams = ref({});
const currentRecord = ref({})

// 接受
const handleAccept = async (record) => {
    try {
        const params = {
            id: record.id,
        };
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + "receiveMachinProcedureFarm",
            params
        );
        SkMessage.success("接受成功");
        fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error("接受失败");
    }
};

//反接受
const handleUnccept = async (record) => {
    try {
        const params = {
            id: record.id,
        };
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + "receptionReceiveMachinProcedureFarm",
            params
        );
        SkMessage.success("反接受成功");
        fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error("反接受失败");
    }
}

// 编号详情
const handleNumberDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        objectId: record.id,
        serviceClassName: record.serviceClassName
    }
}

// 加工单详情
const handleMachiningDetail = (record) => {
    modalTitle.value = '加工单详情'
    modalType.value = 'machinId'
    whetherCustomerData.value = false
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023100300003',
        params: {
            id: record.machinId,
        }
    }
}

// 处理编辑详情数据回显时，刚获取到数据的逻辑
const handleAfterDataLoaded = (formData) => {
    console.log(formData.erpOrderItemList)
    if (!proxy.$util.isNull(formData.erpOrderItemList)) {
        formData.erpOrderItemList.forEach(item => {
            // 下拉框的特殊配置
            item["normsId_config"] = {
                // 自定义类型
                dataType: 1,
                // materialNorms为计量单位的数据
                defaultData: item.materialMation?.materialNorms || []
            }
        })
    }
}

// 工序验收
const handleProcessAcceptance = (record) => {
    modalTitle.value = "工序验收";
    modalType.value = "processAcceptance";
    currentRecord.value = record
    modalVisible.value = true;
    whetherCustomerData.value = true
    operatorParams.value = {
        pageId: "FP2024071500001"
    };
};

// 处理数据保存
const customerDataSave = async (data) => {
    const pageId = operatorParams.value.pageId
    try {
        if (pageId == 'FP2024071500001') {
            // 工序验收
            data.id = null
            data.machinProcedureFarmId = currentRecord.value.id

            await proxy.$http.post(
                proxy.$config.getConfig().erpBasePath + 'writeMachinProcedureAccept',
                data
            )
        } else if (pageId == 'FP2024072600003') {
            // 加工入库
            data.fromId = currentRecord.value.id
            data.id = null
            await proxy.$http.post(
                proxy.$config.getConfig().erpBasePath + 'writeMachinPut',
                data
            )
        }
        handleModalClick(true)
    } catch (error) {
        SkMessage.error(error)
    }
}

// 加工入库
const handleWarehousing = async (record) => {
    modalTitle.value = "加工入库";
    modalType.value = "warehousing";
    const params = {
        id: record.id,
    };
    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + "queryMachinProcedureFarmToInOrOutList",
        params
    );
    whetherCustomerData.value = true
    customerData.value = res.bean
    if (customerData.value.erpOrderItemList.length > 0) {
        customerData.value.erpOrderItemList.forEach(function (item, index) {
            item.unitPrice = item.normsMation.estimatePurchasePrice;
        });
    }
    customerData.value.erpOrderItemList = proxy.$util.erpUtil.calcOrderItem(customerData.value.erpOrderItemList)
    // 调计算总金额函数实现回显
    calcMoney(customerData.value)
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2024072600003",
        params: {
            id: record.id,
        },
    };
};

const showIndexRef = ref(null)

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)
    if (material) {
        // 更新其他相关字段
        record[mationKey] = material
        record["normsId"] = undefined //规格
        record["unitPrice"] = 0 //单价
        // 等待组件挂载完成
        await nextTick()
        // 修改当前行的表格列配置
        showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
            "normsId": {
                dataType: 1,
                defaultData: material.materialNorms
            }
        })
        // 触发 cell-change 事件
        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        // 清空相关字段
        delete record[mationKey]
    }
}

// 处理单元格变化
const handleCellChange = ({ record, dataIndex, value, column }, formData) => {
    // 处理物料选择变化
    if (dataIndex === 'materialId') {
        record.unitPrice = 0
    } else if (dataIndex === 'normsId') {
        const materialNorms = column.getConfig(record).defaultData
        const norms = materialNorms.find(item => item.id === value)
        // 获取物料的预估采购价
        record.unitPrice = norms.normsMation?.estimatePurchasePrice
    }
    // 处理金额计算
    const result = proxy.$util.erpUtil.calcMoneyKey(dataIndex, record)
    Object.assign(record, result)
    calcMoney(formData)
}

// 处理表单项变化
const handleChange = ({ attrKey, formData }) => {
    if (attrKey == 'discount') {
        calcMoney(formData)
    }
}

const calcMoney = (formData) => {
    // 处理金额计算
    const erpOrderItemList = formData.erpOrderItemList || []
    let totalPrice = 0;
    if (!proxy.$util.isNull(erpOrderItemList)) {
        erpOrderItemList.forEach((item) => {
            totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.taxLastMoney)
        })
    }

    // 优惠信息
    const discountMoney = proxy.$util.erpUtil.calcDiscountMoney(formData, totalPrice)
    totalPrice = proxy.$util.calculationUtil.subtraction(totalPrice, discountMoney)
    formData.discountMoney = discountMoney
    formData.totalPrice = totalPrice
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false;
    if (isSubmit) {
        fetchData(); // 刷新表格数据
    }
};

// 初始化
onMounted(async () => {
    await nextTick();

    requestAnimationFrame(() => {
        tableReady.value = true;
    });
    await getInitData();
    await fetchWorkShop();

});
</script>
<style scoped></style>