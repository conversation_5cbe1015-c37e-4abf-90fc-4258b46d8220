<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1720835673573')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">

                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                            <template v-if="!$util.isNull(record.fromId)">
                                <SkTag :color="$util.getTagColor(column.dataIndex)">
                                    转
                                </SkTag>
                            </template>
                        </a>
                    </template>

                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <!-- 流程id点击后的详情 -->
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>

                    <!-- 获取来源类型 -->
                    <template v-if="column.dataIndex === 'fromTypeId'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['wholeOrderOutFromType'], 'id', record.fromTypeId, 'name')">
                        </div>
                    </template>

                    <!-- 获取状态 -->
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['erpOrderStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>

                    <!-- 获取到货状态 -->
                    <template v-if="column.dataIndex === 'otherState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['orderArrivalState'], 'id', record.otherState, 'name')">
                        </div>
                    </template>

                    <!-- 获取质检状态 -->
                    <template v-if="column.dataIndex === 'qualityInspection'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['orderQualityInspectionType'], 'id', record.qualityInspection, 'name')">
                        </div>
                    </template>

                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <!-- 提交审批 -->
                                <a v-if="$config.auth('1721036771903')" @click="handleConfirmOk(record)">
                                    {{ $t('common.submitApproval') }}
                                </a>
                                <SkDivider v-if="$config.auth('1721036771903')" type="vertical" />

                                <!-- 编辑 -->
                                <a v-if="$config.auth('1720835673573')" @click="handleEdit(record, 'edit')">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="$config.auth('1720835673573')" type="vertical" />

                                <!-- 删除 -->
                                <SkPopconfirm v-if="$config.auth('1721036781887')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </template>

                            <template v-if="record.editRow == 2">
                                <!-- 撤销 -->
                                <SkPopconfirm v-if="$config.auth('1721036798487')" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.revoke') }}</a>
                                </SkPopconfirm>
                            </template>

                            <template v-if="(record.state == 'pass' || record.state == 'partiallyCompleted')">
                                <!-- 转采购入库 -->
                                <a v-if="$config.auth('1720836111714') && record.otherState == 1"
                                    @click="wholeOutToPut(record)">
                                    {{ $t('erpProduce.wholeOutList.wholeOutToPut') }}
                                </a>
                                <SkDivider v-if="$config.auth('1720836111714') && record.otherState == 1"
                                    type="vertical" />

                                <!-- 转采购退货 -->
                                <a v-if="$config.auth('1720836053850') && record.otherState == 1"
                                    @click="wholeOutToReturn(record)">
                                    {{ $t('erpProduce.wholeOutList.wholeOutToReturn') }}
                                </a>

                                <!-- 转到货单 -->
                                <a v-if="$config.auth('1720836096049') && (record.otherState == 2 || record.otherState == 3)"
                                    @click="wholeOutToArrival(record)">
                                    {{ $t('erpProduce.wholeOutList.wholeOutToArrival') }}
                                </a>
                            </template>
                        </SkSpace>

                    </template>
                </template>
            </SkTable>

            <!-- 弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details' || modalType === 'wholeOutToPut' || modalType === 'wholeOutToReturn' || modalType === 'wholeOutToArrival'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @customerDataSave="customerDataSave"
                    :whetherCustomerData="whetherCustomerData" :customerData="customerData" @close="handleModalClick"
                    @cell-change="handleCellChange" @loaded="handleShowIndexLoaded"
                    @afterDataLoaded="handleAfterDataLoaded" @handleChange="handleChange">
                    <!-- 传递自定义单元格组件 -->
                    <template #cell-chooseInput-materialId="slotProps">
                        <a-form-item-rest>
                            <SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                    <!-- 交货日期 -->
                    <template #cell-input-deliveryTime="slotProps">
                        <SkDatePicker v-model="slotProps.record[slotProps.column.dataIndex]"
                            :formData="slotProps.record" :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                            :attrKey="slotProps.column.dataIndex" />
                    </template>
                </ShowIndex>
                <!-- 审批人弹窗 -->
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'
import SkDatePicker from '@/components/SkDatePicker/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['wholeOrderOutFromType', 'erpOrderStateEnum', 'orderArrivalState', 'orderQualityInspectionType']);
    initEnumData.value = result
}

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单号',
        dataIndex: 'oddNumber',
        width: 220,
        align: 'center'
    },
    {
        title: '单据日期',
        dataIndex: 'operTime',
        width: 140,
        align: 'center'
    },
    {
        title: '来源单据信息',
        width: 300,
        align: 'center',
        children: [{
            title: '来源类型',
            dataIndex: 'fromTypeId',
            width: 100
        },
        {
            title: '单据编号',
            dataIndex: 'fromId',
            width: 180,
            customRender: ({ record }) => record.fromMation?.oddNumber
        }]
    },
    {
        title: '供应商',
        dataIndex: 'holderMation',
        width: 140,
        customRender: ({ record }) => record.holderMation?.name
    },
    {
        title: '总金额',
        dataIndex: 'totalPrice',
        width: 140,
        align: 'center'
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        width: 140
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 100
    },
    {
        title: '到货状态',
        dataIndex: 'otherState',
        width: 100
    },
    {
        title: '质检状态',
        dataIndex: 'qualityInspection',
        width: 150
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 120,
        align: 'center',
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 160,
        align: 'center'
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 120,
        align: 'center'
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 160,
        align: 'center'
    },
    {
        title: t('common.operation'),
        key: 'action',
        width: 220,
        align: 'center',
        fixed: 'right'
    }
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || '' // 添加关键字搜索参数
        }

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryWholeOrderOutList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

const modalVisible = ref(false)
const modalTitle = ref('审批人选择')
const modalType = ref('approval')
const whetherCustomerData = ref(false)
const customerData = ref({})
const operatorParams = ref({})

// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    whetherCustomerData.value = false
    operatorParams.value = {
        pageId: 'FP2024071300002',
        params: {}
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    whetherCustomerData.value = false
    operatorParams.value = {
        pageId: 'FP2024071300003',
        params: {
            id: record.id
        }
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id,
            serviceClassName: record.serviceClassName
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().erpBasePath + 'erpcommon005',
            params
        )
        SkMessage.success('删除成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 撤销处理
const handleRevoke = async (record) => {
    try {
        const params = {
            processInstanceId: record.processInstanceId,
            serviceClassName: record.serviceClassName
        }

        await proxy.$http.put(
            proxy.$config.getConfig().erpBasePath + 'erpcommon003',
            params
        )

        SkMessage.success('撤销成功')
        await fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    whetherCustomerData.value = false
    operatorParams.value = {
        pageId: 'FP2024071300004',
        params: {
            id: record.id
        }
    }
}

// 转采购入库
const wholeOutToPut = async (record) => {
    modalTitle.value = '转采购入库'
    modalType.value = 'wholeOutToPut'

    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + 'queryWholeOrderOutTransById',
        {
            id: record.id
        })

    whetherCustomerData.value = true
    customerData.value = res.bean

    modalVisible.value = true
    operatorParams.value = {
        // 采购入库的【编辑布局】
        pageId: 'FP2023042300002',
        params: {
            id: record.id
        }
    }
}

// 转采购退货
const wholeOutToReturn = async (record) => {
    modalTitle.value = '转采购退货'
    modalType.value = 'wholeOutToReturn'

    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + 'queryWholeOrderOutTransById',
        {
            id: record.id
        })

    whetherCustomerData.value = true
    customerData.value = res.bean

    modalVisible.value = true
    operatorParams.value = {
        // 采购退货的【编辑布局】
        pageId: 'FP2023042400002',
        params: {
            id: record.id
        }
    }
}

// 转到货单
const wholeOutToArrival = async (record) => {
    modalTitle.value = '转到货单'
    modalType.value = 'wholeOutToArrival'

    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + 'queryWholeOrderOutTransById',
        {
            id: record.id
        })

    whetherCustomerData.value = true
    customerData.value = res.bean

    modalVisible.value = true
    operatorParams.value = {
        // 采购到货【编辑布局】
        pageId: 'FP2024061100002',
        params: {
            id: record.id
        }
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData() // 刷新表格数据
    }
}

// 当前行记录
const currentRecord = ref(null)

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        // 先关闭 Popconfirm
        await nextTick()

        // 打开审批人选择弹窗
        currentRecord.value = record
        modalTitle.value = '审批人选择'
        modalType.value = 'approval'
        modalVisible.value = true
    } catch (error) {
        SkMessage.error('操作失败')
    }
}

// 处理数据保存
const customerDataSave = async (data) => {
    const pageId = operatorParams.value.pageId
    if (pageId == 'FP2023042300002') {
        // 转采购入库
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'insertWholeOrderOutToTurnPut',
            data
        )
    } else if (pageId == 'FP2023042400002') {
        // 转采购退货
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'insertWholeOrderOutToReturns',
            data
        )
    } else if (pageId == 'FP2024061100002') {
        // 转到货单
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'insertWholeOrderOutToTurnDelivery',
            data
        )
    }
    handleModalClick(true)
}

// 处理弹窗取消
const handleModalCancel = async () => {
    try {
        await nextTick()
        modalVisible.value = false
    } catch (error) {
        SkMessage.error('处理失败')
    }
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        // 构建参数，与旧系统保持一致
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id,
            serviceClassName: currentRecord.value.serviceClassName
        }

        // 发送提交请求
        await proxy.$http.put(
            proxy.$config.getConfig().erpBasePath + 'erpcommon006',
            params
        )

        // 提交成功
        SkMessage.success('提交成功')
        // 关闭弹窗
        modalVisible.value = false
        // 刷新数据
        await fetchData()

    } catch (error) {
        SkMessage.error('提交失败')
    }
}

const showIndexRef = ref(null)

// 处理数据回显示，刚获取到数据的逻辑
const handleAfterDataLoaded = (formData) => {
    // 这里可以处理特殊的参数
    const pageId = operatorParams.value.pageId
    const notEdit = proxy.$config.formEditType.notEdit

    if (!proxy.$util.isNull(formData.erpOrderItemList)) {
        formData.erpOrderItemList.forEach(item => {
            // 下拉框的特殊配置
            item[`normsId_config`] = {
                // 自定义类型
                dataType: 1,
                // materialNorms为计量单位的数据
                defaultData: item.materialMation?.materialNorms || []
            }
            if (!proxy.$util.isNull(formData.fromId)) {
                if (pageId == 'FP2024071300003') {
                    item[`materialId_config`] = { isEdit: notEdit }
                    item[`normsId_config`].isEdit = notEdit
                }
                else {
                    item[`materialId_config`] = { isEdit: notEdit }
                    item[`normsId_config`].isEdit = notEdit
                    item[`unitPrice_config`] = { isEdit: notEdit }
                    item[`allPrice_config`] = { isEdit: notEdit }
                    item[`taxRate_config`] = { isEdit: notEdit }
                    item[`taxMoney_config`] = { isEdit: notEdit }
                    item[`taxUnitPrice_config`] = { isEdit: notEdit }
                    item[`taxLastMoney_config`] = { isEdit: notEdit }
                    if (pageId == 'FP2024061100002') {
                        item[`qualityInspection_config`] = { isEdit: notEdit }
                        item[`qualityInspectionRatio_config`] = { isEdit: notEdit }
                    }
                }
            } else {
                // 如果没用单据来源，但是要转采购入库或转到货单或转采购退货单时设置禁用
                if (pageId == 'FP2023042300002' || pageId == 'FP2024061100002' || pageId ==
                    'FP2023042400002') {
                    item[`materialId_config`] = { isEdit: notEdit }
                    item[`normsId_config`].isEdit = notEdit
                    item[`unitPrice_config`] = { isEdit: notEdit }
                    item[`allPrice_config`] = { isEdit: notEdit }
                    item[`taxRate_config`] = { isEdit: notEdit }
                    item[`taxMoney_config`] = { isEdit: notEdit }
                    item[`taxUnitPrice_config`] = { isEdit: notEdit }
                    item[`taxLastMoney_config`] = { isEdit: notEdit }
                }
                if (pageId == 'FP2024061100002') {
                    item[`qualityInspection_config`] = { isEdit: notEdit }
                    item[`qualityInspectionRatio_config`] = { isEdit: notEdit }
                }

            }
        })
    }
}

// 处理组件加载完成
const handleShowIndexLoaded = () => {
    const pageId = operatorParams.value.pageId

    // 在组件加载完成后执行需要的操作，转采购入库、采购退货、采购到货的【编辑布局】
    if (pageId == 'FP2023042300002' || pageId == 'FP2023042400002' || pageId == 'FP2024061100002') {
        showIndexRef.value?.writeComponentRef?.updateShowAdd("erpOrderItemList", false)
    }
}

// 处理单元格变化
const handleCellChange = async ({ record, dataIndex, value, column }, formData) => {
    // 处理物料选择变化
    if (dataIndex == 'materialId') {
        record.unitPrice = 0
    } else if (dataIndex == 'normsId') {
        const materialNorms = column.getConfig(record).defaultData
        const norms = materialNorms.find(item => item.id === value)

        // 获取物料的预估采购价
        record.unitPrice = norms.estimatePurchasePrice
    }
    // 处理金额计算
    const result = proxy.$util.erpUtil.calcMoneyKey(dataIndex, record)
    // 将result合入到record中
    Object.assign(record, result)

    calcMoney(formData)
}

// 处理表单项变化
const handleChange = ({ attrKey, formData }) => {
    if (attrKey == 'discount') {
        calcMoney(formData)
    }
}

const calcMoney = (formData) => {
    // 处理金额计算
    const erpOrderItemList = formData?.erpOrderItemList || []
    let totalPrice = 0;
    if (!proxy.$util.isNull(erpOrderItemList)) {
        erpOrderItemList.forEach((item, i) => {
            totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.taxLastMoney)
        })
    }

    // 优惠信息
    const discountMoney = proxy.$util.erpUtil.calcDiscountMoney(formData, totalPrice)
    totalPrice = proxy.$util.calculationUtil.subtraction(totalPrice, discountMoney)
    formData.discountMoney = discountMoney
    formData.totalPrice = totalPrice
}

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

    if (material) {
        // 更新其他相关字段
        record[mationKey] = material
        record["normsId"] = undefined
        record["unitPrice"] = 0

        // 等待组件挂载完成
        await nextTick()

        // 修改当前行的表格列配置
        showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
            "normsId": {
                dataType: 1,
                defaultData: material.materialNorms
            }
        })

        // 触发 cell-change 事件
        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        // 清空相关字段
        delete record[mationKey]
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    await fetchData()
})

</script>
<style></style>