<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.material.dao.MaterialDao">

    <select id="queryMaterialInventoryWarningList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
			*
		FROM
			(SELECT
				b.id,
				b.`name`,
				b.model,
				b.category_id categoryId,
				b.unit,
				b.`type`,
				b.from_type fromType,
				a.safety_tock safetyTock,
				a.name normsName,
				IFNULL((SELECT SUM(it.stock)
				FROM erp_material_norms_stock it
				WHERE it.norms_id = a.id
				), 0) allStock
			FROM
				erp_material_norms a,
				erp_material b
			<where>
				b.delete_flag = #{deleteFlag}
				AND b.enabled = #{enabled}
				AND a.enabled = #{enabled}
				AND a.material_id = b.id
				<if test="sqlExtract != '' and sqlExtract != null">
					${sqlExtract}
				</if>
				<if test="keyword != null and keyword != ''">
					AND (b.`name` like '%${keyword}%' OR b.model LIKE '%${keyword}%')
				</if>
			</where>
	        ORDER BY b.create_time DESC) a
		WHERE a.safetyTock > a.allStock
    </select>

</mapper>