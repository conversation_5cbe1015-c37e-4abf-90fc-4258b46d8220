<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.depot.dao.DepotLevelValDao">

    <select id="queryAllChildIdsByParentId" resultType="java.lang.String">
		<foreach collection="ids" item="id" index="idx" separator="UNION">
			SELECT
				d.id
			FROM
				(
					SELECT
						@ids${idx} AS _ids,
						(
							SELECT
								@ids${idx} := GROUP_CONCAT(id)
							FROM
								erp_depot_level_val
							WHERE FIND_IN_SET(parent_id, @ids${idx})
						) AS cids,
						@l${idx} := @l${idx} + 1 AS level
					FROM
						erp_depot_level_val,
						(
							SELECT
								@ids${idx} := #{id},
								@l${idx} := 0
						) b
					WHERE @ids${idx} IS NOT NULL
				) i,
				erp_depot_level_val d
			WHERE FIND_IN_SET(d.id, i._ids)
		</foreach>
	</select>
    
</mapper>