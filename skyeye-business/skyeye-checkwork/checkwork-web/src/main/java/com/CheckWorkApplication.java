/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableAutoConfiguration(exclude = {
    org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
})
@EnableTransactionManagement//启注解事务管理，等同于xml配置方式的 <tx:annotation-driven />
@ComponentScan(basePackages = {"com.skyeye"})
@EnableDiscoveryClient // 开启服务发现
@EnableFeignClients
public class CheckWorkApplication {

    public static void main(String[] args) {
        System.out.println("=== CheckWorkApplication 开始启动 ===");
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("工作目录: " + System.getProperty("user.dir"));

        try {
            System.setProperty("spring.devtools.restart.enabled", "false");
            System.out.println("=== 准备启动Spring Boot应用 ===");
            SpringApplication.run(CheckWorkApplication.class, args);
            System.out.println("=== CheckWorkApplication 启动完成 ===");
        } catch (Exception e) {
            System.err.println("=== CheckWorkApplication 启动失败 ===");
            e.printStackTrace();
            throw e;
        }
    }

}
