### set log levels这里的INFO,Stdout,D,E可以理解为变量，也可以说是输出平台，在下面我们可以看到 ###  
log4j.rootLogger = INFO, C, D, E

### console控制台输出 ###
log4j.appender.C = org.apache.log4j.ConsoleAppender  
### System.out也就是输出 out输出是黑色字体，err输出的字体是红色 ###
log4j.appender.C.Target = System.out  
### layout是指布局，也就是说输出日志信息的格式样式，在这里我们使用的是log4j提供的 ###
log4j.appender.C.layout = org.apache.log4j.PatternLayout 
### 这里就是指定我们日志文件以哪一种格式去输出 ###
log4j.appender.C.layout.ConversionPattern = [skyeye-promote][%p] [%-d{yyyy-MM-dd HH:mm:ss}] %C.%M(%L) | %m%n  


### log file INFO级别输出日志文件 ###
log4j.appender.D = org.apache.log4j.DailyRollingFileAppender  
### 指定日志输出位置 ###
log4j.appender.D.File = ../logs/skyeye.log  
### 这个的意思是指是追加还是覆盖 默认是 true  true是追加 false是覆盖 ###
log4j.appender.D.Append = true  
### 这个是指日志输出的级别在这里指定的是 INFO级别 ###
log4j.appender.D.Threshold = INFO   
### layout是指布局，也就是说输出日志信息的格式样式，在这里我们使用的是log4j提供的 ###
log4j.appender.D.layout = org.apache.log4j.PatternLayout  
### 这里就是指定我们日志文件以哪一种格式去输出 ###
log4j.appender.D.layout.ConversionPattern = [skyeye-promote][%p] [%-d{yyyy-MM-dd HH:mm:ss}] %C.%M(%L) | %m%n  


### exception ERROR级别输出日志文件 ###
#这个跟上面一样 只不过是日志级别是 ERROR级的，方便我们直接查看系统异常信息
log4j.appender.E = org.apache.log4j.DailyRollingFileAppender
log4j.appender.E.File = ../logs/skyeye_error.log
log4j.appender.E.Append = true
log4j.appender.E.Threshold = ERROR
log4j.appender.E.layout = org.apache.log4j.PatternLayout
log4j.appender.E.layout.ConversionPattern = [skyeye-promote][%p] [%-d{yyyy-MM-dd HH:mm:ss}] %C.%M(%L) | %m%n  

#localhost日志文件输出级别为INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].level = INFO
#localhost日志文件输出处理类2localhost.org.apache.juli.FileHandler
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].handlers = 2localhost.org.apache.juli.FileHandler

#manager日志文件输出级别为INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].level = INFO
#manager日志文件输出处理类3manager.org.apache.juli.FileHandler
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].handlers = 3manager.org.apache.juli.FileHandler

#host-manager日志文件输出级别为INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].level = INFO
#host-manager日志文件输出处理类4host-manager.org.apache.juli.FileHandler
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].handlers = 4host-manager.org.apache.juli.FileHandler

#设置包名的输出级别
log4j.logger.com.skyeye.common.filter=info
